-- Database Schema for Sale App - CTV Registration
-- Generated for Collaborator Registration Flow
-- Database: PostgreSQL

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing objects if they exist (for clean re-run)
DROP TABLE IF EXISTS user_session CASCADE;
DROP TABLE IF EXISTS registration_history CASCADE;
DROP TABLE IF EXISTS identity_document CASCADE;
DROP TABLE IF EXISTS collaborator_registration CASCADE;
DROP TABLE IF EXISTS referrer CASCADE;
DROP TABLE IF EXISTS branch CASCADE;

-- Drop existing types if they exist
DROP TYPE IF EXISTS registration_status CASCADE;
DROP TYPE IF EXISTS verification_status CASCADE;
DROP TYPE IF EXISTS branch_status CASCADE;
DROP TYPE IF EXISTS referrer_status CASCADE;
DROP TYPE IF EXISTS history_action CASCADE;
DROP TYPE IF EXISTS session_status CASCADE;

-- Create ENU<PERSON> types
CREATE TYPE registration_status AS ENUM (
    'DRAFT',            -- <PERSON><PERSON> soạn thảo - có thể làm lại từ đầu
    'PENDING_APPROVAL', -- <PERSON><PERSON> duyệt - không được làm lại
    'APPROVED',         -- Đã duyệt
    'REJECTED'          -- Bị từ chối
);

CREATE TYPE verification_status AS ENUM (
    'PENDING',          -- Chờ xác thực
    'VERIFIED',         -- Đã xác thực
    'FAILED'            -- Xác thực thất bại
);

CREATE TYPE branch_status AS ENUM (
    'ACTIVE',           -- Hoạt động
    'INACTIVE'          -- Không hoạt động
);

CREATE TYPE referrer_status AS ENUM (
    'ACTIVE',           -- Hoạt động
    'INACTIVE'          -- Không hoạt động
);

CREATE TYPE history_action AS ENUM (
    'CREATED',          -- Tạo mới
    'UPDATED',          -- Cập nhật
    'SUBMITTED',        -- Nộp đăng ký
    'APPROVED',         -- Duyệt
    'REJECTED'          -- Từ chối
);

CREATE TYPE session_status AS ENUM (
    'ACTIVE',           -- Đang hoạt động
    'EXPIRED'           -- Đã hết hạn
);

-- Create tables

-- Master data chi nhánh
CREATE TABLE branch (
    branch_code VARCHAR(10) PRIMARY KEY,
    branch_name VARCHAR(200) NOT NULL,
    address VARCHAR(500) NOT NULL,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    manager_id VARCHAR(36),
    status branch_status NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

COMMENT ON TABLE branch IS 'Master data chi nhánh';
COMMENT ON COLUMN branch.branch_code IS 'Mã chi nhánh';
COMMENT ON COLUMN branch.branch_name IS 'Tên chi nhánh';
COMMENT ON COLUMN branch.address IS 'Địa chỉ chi nhánh';
COMMENT ON COLUMN branch.phone IS 'Số điện thoại chi nhánh';
COMMENT ON COLUMN branch.email IS 'Email chi nhánh';
COMMENT ON COLUMN branch.manager_id IS 'ID quản lý chi nhánh';
COMMENT ON COLUMN branch.status IS 'Trạng thái chi nhánh';
COMMENT ON COLUMN branch.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN branch.updated_at IS 'Thời gian cập nhật';

-- Bảng thông tin người giới thiệu
CREATE TABLE referrer (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(100) NOT NULL,
    employee_code VARCHAR(20) NOT NULL UNIQUE,
    phone_number VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    branch_code VARCHAR(10) NOT NULL,
    status referrer_status NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT fk_referrer_branch FOREIGN KEY (branch_code) REFERENCES branch(branch_code)
);

COMMENT ON TABLE referrer IS 'Bảng thông tin người giới thiệu';
COMMENT ON COLUMN referrer.id IS 'UUID primary key';
COMMENT ON COLUMN referrer.full_name IS 'Tên người giới thiệu';
COMMENT ON COLUMN referrer.employee_code IS 'Mã nhân viên';
COMMENT ON COLUMN referrer.phone_number IS 'Số điện thoại';
COMMENT ON COLUMN referrer.email IS 'Email';
COMMENT ON COLUMN referrer.branch_code IS 'Mã chi nhánh';
COMMENT ON COLUMN referrer.status IS 'Trạng thái';
COMMENT ON COLUMN referrer.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN referrer.updated_at IS 'Thời gian cập nhật';

-- Bảng chính lưu thông tin đăng ký CTV
CREATE TABLE collaborator_registration (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(100) NOT NULL,
    id_number VARCHAR(20) NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    issue_place VARCHAR(200) NOT NULL,
    permanent_address TEXT NOT NULL,
    phone_number VARCHAR(15) NOT NULL,
    internal_email VARCHAR(100),
    branch_code VARCHAR(10) NOT NULL,
    branch_name VARCHAR(200) NOT NULL,
    referrer_id VARCHAR(36),
    registration_status registration_status NOT NULL DEFAULT 'DRAFT',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    submitted_at TIMESTAMP,
    created_by VARCHAR(36) NOT NULL,
    approved_by VARCHAR(36),
    approved_at TIMESTAMP,
    rejection_reason TEXT,
    
    CONSTRAINT fk_ctv_branch FOREIGN KEY (branch_code) REFERENCES branch(branch_code),
    CONSTRAINT fk_ctv_referrer FOREIGN KEY (referrer_id) REFERENCES referrer(id)
);

COMMENT ON TABLE collaborator_registration IS 'Bảng chính lưu thông tin đăng ký CTV';
COMMENT ON COLUMN collaborator_registration.id IS 'UUID primary key';
COMMENT ON COLUMN collaborator_registration.full_name IS 'Tên đầy đủ từ GTTT';
COMMENT ON COLUMN collaborator_registration.id_number IS 'Số GTTT';
COMMENT ON COLUMN collaborator_registration.issue_date IS 'Ngày cấp GTTT';
COMMENT ON COLUMN collaborator_registration.expiry_date IS 'Ngày hết hạn GTTT';
COMMENT ON COLUMN collaborator_registration.issue_place IS 'Nơi cấp GTTT';
COMMENT ON COLUMN collaborator_registration.permanent_address IS 'Địa chỉ thường trú';
COMMENT ON COLUMN collaborator_registration.phone_number IS 'Số điện thoại liên hệ';
COMMENT ON COLUMN collaborator_registration.internal_email IS 'Email nội bộ (tùy chọn)';
COMMENT ON COLUMN collaborator_registration.branch_code IS 'Mã chi nhánh';
COMMENT ON COLUMN collaborator_registration.branch_name IS 'Tên chi nhánh';
COMMENT ON COLUMN collaborator_registration.referrer_id IS 'ID người giới thiệu (tùy chọn)';
COMMENT ON COLUMN collaborator_registration.registration_status IS 'Trạng thái đăng ký';
COMMENT ON COLUMN collaborator_registration.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN collaborator_registration.updated_at IS 'Thời gian cập nhật';
COMMENT ON COLUMN collaborator_registration.submitted_at IS 'Thời gian submit đăng ký';
COMMENT ON COLUMN collaborator_registration.created_by IS 'User tạo đăng ký';
COMMENT ON COLUMN collaborator_registration.approved_by IS 'User duyệt đăng ký';
COMMENT ON COLUMN collaborator_registration.approved_at IS 'Thời gian duyệt';
COMMENT ON COLUMN collaborator_registration.rejection_reason IS 'Lý do từ chối (nếu có)';

-- Bảng lưu thông tin giấy tờ tùy thân
CREATE TABLE identity_document (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36) NOT NULL,
    front_image_url VARCHAR(500) NOT NULL,
    back_image_url VARCHAR(500) NOT NULL,
    qr_data TEXT,
    extracted_info JSON,
    verification_status verification_status NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    verified_at TIMESTAMP,
    
    CONSTRAINT fk_identity_registration FOREIGN KEY (registration_id) REFERENCES collaborator_registration(id) ON DELETE CASCADE
);

COMMENT ON TABLE identity_document IS 'Bảng lưu thông tin giấy tờ tùy thân';
COMMENT ON COLUMN identity_document.id IS 'UUID primary key';
COMMENT ON COLUMN identity_document.registration_id IS 'ID đăng ký CTV';
COMMENT ON COLUMN identity_document.front_image_url IS 'URL ảnh mặt trước GTTT';
COMMENT ON COLUMN identity_document.back_image_url IS 'URL ảnh mặt sau GTTT';
COMMENT ON COLUMN identity_document.qr_data IS 'Dữ liệu QR code đã quét';
COMMENT ON COLUMN identity_document.extracted_info IS 'Thông tin trích xuất từ QR';
COMMENT ON COLUMN identity_document.verification_status IS 'Trạng thái xác thực';
COMMENT ON COLUMN identity_document.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN identity_document.verified_at IS 'Thời gian xác thực';

-- Bảng lịch sử thay đổi trạng thái đăng ký
CREATE TABLE registration_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36) NOT NULL,
    action history_action NOT NULL,
    old_status registration_status,
    new_status registration_status NOT NULL,
    notes TEXT,
    performed_by VARCHAR(36) NOT NULL,
    performed_at TIMESTAMP NOT NULL DEFAULT NOW(),

    CONSTRAINT fk_history_registration FOREIGN KEY (registration_id) REFERENCES collaborator_registration(id) ON DELETE CASCADE
);

COMMENT ON TABLE registration_history IS 'Bảng lịch sử thay đổi trạng thái đăng ký';
COMMENT ON COLUMN registration_history.id IS 'UUID primary key';
COMMENT ON COLUMN registration_history.registration_id IS 'ID đăng ký';
COMMENT ON COLUMN registration_history.action IS 'Hành động thực hiện';
COMMENT ON COLUMN registration_history.old_status IS 'Trạng thái cũ';
COMMENT ON COLUMN registration_history.new_status IS 'Trạng thái mới';
COMMENT ON COLUMN registration_history.notes IS 'Ghi chú';
COMMENT ON COLUMN registration_history.performed_by IS 'Người thực hiện';
COMMENT ON COLUMN registration_history.performed_at IS 'Thời gian thực hiện';

-- Bảng quản lý phiên làm việc
CREATE TABLE user_session (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36),
    device_id VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    status session_status NOT NULL DEFAULT 'ACTIVE',

    CONSTRAINT fk_session_registration FOREIGN KEY (registration_id) REFERENCES collaborator_registration(id) ON DELETE SET NULL
);

COMMENT ON TABLE user_session IS 'Bảng quản lý phiên làm việc';
COMMENT ON COLUMN user_session.id IS 'UUID primary key';
COMMENT ON COLUMN user_session.registration_id IS 'ID đăng ký (nếu có)';
COMMENT ON COLUMN user_session.device_id IS 'ID thiết bị';
COMMENT ON COLUMN user_session.ip_address IS 'Địa chỉ IP';
COMMENT ON COLUMN user_session.user_agent IS 'Thông tin trình duyệt';
COMMENT ON COLUMN user_session.created_at IS 'Thời gian tạo';
COMMENT ON COLUMN user_session.last_activity IS 'Hoạt động cuối';
COMMENT ON COLUMN user_session.status IS 'Trạng thái phiên';

-- Create indexes for better performance

-- Indexes for branch table
CREATE INDEX idx_branch_name ON branch(branch_name);
CREATE INDEX idx_branch_status ON branch(status);

-- Indexes for referrer table
CREATE INDEX idx_referrer_code ON referrer(employee_code);
CREATE INDEX idx_referrer_branch ON referrer(branch_code);
CREATE INDEX idx_referrer_status ON referrer(status);

-- Indexes for collaborator_registration table
CREATE INDEX idx_ctv_id_number ON collaborator_registration(id_number);
CREATE INDEX idx_ctv_phone ON collaborator_registration(phone_number);
CREATE INDEX idx_ctv_status ON collaborator_registration(registration_status);
CREATE INDEX idx_ctv_branch ON collaborator_registration(branch_code);
CREATE INDEX idx_ctv_created ON collaborator_registration(created_at);

-- Indexes for identity_document table
CREATE INDEX idx_identity_registration ON identity_document(registration_id);
CREATE INDEX idx_identity_status ON identity_document(verification_status);

-- Indexes for registration_history table
CREATE INDEX idx_history_registration ON registration_history(registration_id);
CREATE INDEX idx_history_action ON registration_history(action);
CREATE INDEX idx_history_time ON registration_history(performed_at);

-- Indexes for user_session table
CREATE INDEX idx_session_registration ON user_session(registration_id);
CREATE INDEX idx_session_device ON user_session(device_id);
CREATE INDEX idx_session_status ON user_session(status);
CREATE INDEX idx_session_activity ON user_session(last_activity);

-- Create triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at column
CREATE TRIGGER update_branch_updated_at BEFORE UPDATE ON branch
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_referrer_updated_at BEFORE UPDATE ON referrer
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ctv_updated_at BEFORE UPDATE ON collaborator_registration
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Business rules and constraints (implemented as check constraints and functions)

-- Constraint: Ensure expiry_date is after issue_date
ALTER TABLE collaborator_registration
ADD CONSTRAINT chk_ctv_date_validity
CHECK (expiry_date > issue_date);

-- Constraint: Ensure phone number format (basic validation)
ALTER TABLE collaborator_registration
ADD CONSTRAINT chk_ctv_phone_format
CHECK (phone_number ~ '^[0-9+\-\s()]+$');

ALTER TABLE referrer
ADD CONSTRAINT chk_referrer_phone_format
CHECK (phone_number ~ '^[0-9+\-\s()]+$');

-- Constraint: Ensure email format
ALTER TABLE collaborator_registration
ADD CONSTRAINT chk_ctv_email_format
CHECK (internal_email IS NULL OR internal_email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE referrer
ADD CONSTRAINT chk_referrer_email_format
CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE branch
ADD CONSTRAINT chk_branch_email_format
CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Function to validate business rules before status change
CREATE OR REPLACE FUNCTION validate_registration_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Rule: Cannot modify registration when status is PENDING_APPROVAL or higher
    IF OLD.registration_status IN ('PENDING_APPROVAL', 'APPROVED', 'REJECTED')
       AND NEW.registration_status != OLD.registration_status THEN
        -- Only allow status changes, not data changes
        IF (OLD.full_name, OLD.id_number, OLD.issue_date, OLD.expiry_date,
            OLD.issue_place, OLD.permanent_address, OLD.phone_number,
            OLD.internal_email, OLD.branch_code, OLD.branch_name, OLD.referrer_id)
           !=
           (NEW.full_name, NEW.id_number, NEW.issue_date, NEW.expiry_date,
            NEW.issue_place, NEW.permanent_address, NEW.phone_number,
            NEW.internal_email, NEW.branch_code, NEW.branch_name, NEW.referrer_id) THEN
            RAISE EXCEPTION 'Cannot modify registration data when status is % or higher', OLD.registration_status;
        END IF;
    END IF;

    -- Rule: Set submitted_at when status changes to PENDING_APPROVAL
    IF NEW.registration_status = 'PENDING_APPROVAL' AND OLD.registration_status = 'DRAFT' THEN
        NEW.submitted_at = NOW();
    END IF;

    -- Rule: Set approved_at when status changes to APPROVED
    IF NEW.registration_status = 'APPROVED' AND OLD.registration_status = 'PENDING_APPROVAL' THEN
        NEW.approved_at = NOW();
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply business rule trigger
CREATE TRIGGER trg_validate_registration_status
    BEFORE UPDATE ON collaborator_registration
    FOR EACH ROW EXECUTE FUNCTION validate_registration_status_change();

-- Insert sample data for testing

-- Sample branches
INSERT INTO branch (branch_code, branch_name, address, phone, email, manager_id) VALUES
('HN001', 'Chi nhánh Hà Nội 1', '123 Đường ABC, Quận Ba Đình, Hà Nội', '024-1234567', '<EMAIL>', uuid_generate_v4()),
('HCM001', 'Chi nhánh TP.HCM 1', '456 Đường XYZ, Quận 1, TP.HCM', '028-7654321', '<EMAIL>', uuid_generate_v4()),
('DN001', 'Chi nhánh Đà Nẵng 1', '789 Đường DEF, Quận Hải Châu, Đà Nẵng', '0236-9876543', '<EMAIL>', uuid_generate_v4());

-- Sample referrers
INSERT INTO referrer (full_name, employee_code, phone_number, email, branch_code) VALUES
('Nguyễn Văn A', 'EMP001', '0901234567', '<EMAIL>', 'HN001'),
('Trần Thị B', 'EMP002', '0907654321', '<EMAIL>', 'HCM001'),
('Lê Văn C', 'EMP003', '0909876543', '<EMAIL>', 'DN001');

/*
BUSINESS RULES SUMMARY:
1. Chỉ cho phép làm lại đăng ký khi registration_status = 'DRAFT'
2. Không cho phép sửa đổi khi registration_status = 'PENDING_APPROVAL' trở lên
3. Bắt buộc có identity_document trước khi submit (chuyển sang PENDING_APPROVAL)
4. Mỗi registration chỉ có 1 identity_document
5. referrer_id là tùy chọn (nullable)
6. internal_email là tùy chọn (nullable)
7. Audit trail đầy đủ thông qua registration_history
8. Session management hỗ trợ tính năng "làm lại từ đầu"
9. Automatic timestamp updates via triggers
10. Data validation via check constraints
11. Foreign key relationships ensure data integrity
*/
