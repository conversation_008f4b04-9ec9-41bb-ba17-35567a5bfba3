// Sale App - User Info Approach (Self-Join)
// Alternative design with single user_info table

Project SaleApp_UserInfo {
  database_type: 'PostgreSQL'
  Note: '''
    # Sale App - User Info Approach
    
    **Single Table Design:**
    - Tất cả user (CTV + Manager) trong 1 bảng user_info
    - Self-join để thể hiện hierarchy quản lý
    - Role-based fields với nullable columns
  '''
}

// ===== ENUM DEFINITIONS =====
Enum registration_status {
  DRAFT [note: '🟡 Đang soạn thảo']
  PENDING_APPROVAL [note: '🟠 Chờ duyệt']
  APPROVED [note: '🟢 Đã duyệt']
  REJECTED [note: '🔴 Bị từ chối']
}

Enum user_role {
  COLLABORATOR [note: '👤 Cộng tác viên']
  MANAGER [note: '👨‍💼 Quản lý']
  BRANCH_MANAGER [note: '🏢 Giám đốc chi nhánh']
  ADMIN [note: '⚙️ Quản trị viên']
}

Enum user_status {
  ACTIVE [note: '🟢 Đang hoạt động']
  SUSPENDED [note: '🟡 Tạm ngưng']
  TERMINATED [note: '🔴 Chấm dứt']
  ON_LEAVE [note: '🟠 Nghỉ phép']
}

Enum branch_status {
  ACTIVE [note: '✅ Hoạt động']
  INACTIVE [note: '❌ Không hoạt động']
}

// ===== CORE TABLES =====

Table branch {
  branch_code varchar(10) [pk, note: '🏢 Mã chi nhánh']
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  address varchar(500) [not null, note: 'Địa chỉ chi nhánh']
  branch_manager_id varchar(36) [note: 'ID giám đốc chi nhánh']
  status branch_status [not null, default: 'ACTIVE']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
}

Table user_info {
  id varchar(36) [pk, note: '👤 UUID user']
  registration_id varchar(36) [unique, note: 'Liên kết với đăng ký (chỉ CTV)']
  
  // ===== THÔNG TIN CHUNG (TẤT CẢ ROLES) =====
  full_name varchar(100) [not null, note: 'Họ tên đầy đủ']
  id_card_no varchar(20) [not null, unique, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  phone_number varchar(15) [not null, note: 'Số điện thoại']
  email varchar(100) [not null, note: 'Email']
  
  // ===== THÔNG TIN CÔNG VIỆC =====
  employee_code varchar(20) [not null, unique, note: 'Mã nhân viên']
  role user_role [not null, note: 'Vai trò trong hệ thống']
  position varchar(100) [note: 'Chức vụ']
  department varchar(100) [note: 'Phòng ban']
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Chi nhánh']
  manager_id varchar(36) [ref: > user_info.id, note: '👨‍💼 Quản lý trực tiếp (SELF-JOIN)']
  
  // ===== THÔNG TIN CTV (CHỈ ROLE = COLLABORATOR) =====
  core_account_no varchar(20) [unique, note: '💳 Số tài khoản core (chỉ CTV)']
  guarantee_limit decimal(15,2) [default: 0, note: '💰 Hạn mức bảo lãnh (chỉ CTV)']
  outstanding_balance decimal(15,2) [default: 0, note: '📊 Dư nợ còn lại (chỉ CTV)']
  collateral_value decimal(15,2) [default: 0, note: '🏠 Giá trị tài sản thế chấp (chỉ CTV)']
  
  // ===== THÔNG TIN MANAGER (CHỈ ROLE = MANAGER/BRANCH_MANAGER) =====
  max_ctv_limit int [default: 0, note: 'Số CTV tối đa (chỉ Manager)']
  current_ctv_count int [default: 0, note: 'Số CTV hiện tại (chỉ Manager)']
  approval_limit decimal(15,2) [default: 0, note: 'Hạn mức phê duyệt (chỉ Manager)']
  can_approve_ctv boolean [default: false, note: 'Quyền duyệt CTV (chỉ Manager)']
  can_modify_guarantee boolean [default: false, note: 'Quyền sửa hạn mức (chỉ Manager)']
  
  // ===== THÔNG TIN CHUNG =====
  status user_status [not null, default: 'ACTIVE']
  start_date date [not null, note: 'Ngày bắt đầu']
  end_date date [note: 'Ngày kết thúc']
  
  // Audit fields
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null]
  updated_by varchar(36)
  
  indexes {
    employee_code [name: 'idx_user_employee_code']
    id_card_no [name: 'idx_user_id_card']
    core_account_no [name: 'idx_user_core_account']
    role [name: 'idx_user_role']
    branch_code [name: 'idx_user_branch']
    manager_id [name: 'idx_user_manager']
    status [name: 'idx_user_status']
    registration_id [name: 'idx_user_registration']
  }
  
  Note: '''
    👤 **Bảng thông tin user tổng hợp**
    
    **Thiết kế Single Table:**
    - Tất cả user (CTV + Manager) trong 1 bảng
    - Self-join: manager_id → user_info.id
    - Role-based fields (một số cột chỉ áp dụng cho role cụ thể)
    
    **Ưu điểm:**
    ✅ Cấu trúc đơn giản, hierarchy tự nhiên
    ✅ Dễ chuyển đổi vai trò
    ✅ Query hierarchy đơn giản
    
    **Lưu ý:**
    ⚠️ Nhiều nullable fields theo role
    ⚠️ Cần validation logic phức tạp
    ⚠️ Business rules phụ thuộc role
  '''
}

Table collaborator_registration {
  id varchar(36) [pk, note: '📋 UUID đăng ký CTV']
  
  // Thông tin từ GTTT
  full_name varchar(100) [not null, note: 'Tên đầy đủ từ GTTT']
  id_card_no varchar(20) [not null, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  
  // Thông tin liên hệ
  phone_number varchar(15) [not null, note: 'Số điện thoại liên hệ']
  internal_email varchar(100) [note: 'Email nội bộ (tùy chọn)']
  
  // Thông tin chi nhánh
  branch_code varchar(10) [not null, ref: > branch.branch_code]
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  referrer_code varchar(36) [note: 'Người giới thiệu (tùy chọn)']
  
  // Trạng thái
  status registration_status [not null, default: 'DRAFT']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  submitted_at timestamp [note: 'Thời gian submit']
  
  // Xử lý
  created_by varchar(36) [not null, note: 'User tạo đăng ký']
  approved_by varchar(36) [note: 'User duyệt đăng ký']
  approved_at timestamp [note: 'Thời gian duyệt']
  rejection_reason text [note: 'Lý do từ chối']
  
  Note: '''
    📋 **Đăng ký CTV**
    
    Sau khi APPROVED → Tạo record trong user_info
    với role = 'COLLABORATOR'
  '''
}

// ===== RELATIONSHIP =====
Ref: user_info.registration_id - collaborator_registration.id [note: 'CTV được tạo từ đăng ký']
Ref: branch.branch_manager_id > user_info.id [note: 'Giám đốc chi nhánh']

// ===== BUSINESS RULES =====
Note business_rules {
  '''
  ## 📋 QUY TẮC NGHIỆP VỤ - USER INFO APPROACH
  
  ### 🎭 Role-based Validation:
  **COLLABORATOR:**
  - Bắt buộc: core_account_no, registration_id
  - Tùy chọn: guarantee_limit, outstanding_balance, collateral_value
  - Không áp dụng: max_ctv_limit, approval_limit, can_*
  
  **MANAGER/BRANCH_MANAGER:**
  - Bắt buộc: max_ctv_limit, approval_limit
  - Tùy chọn: can_approve_ctv, can_modify_guarantee
  - Không áp dụng: core_account_no, guarantee_limit
  
  ### 🔄 Self-Join Hierarchy:
  - manager_id → user_info.id (cùng bảng)
  - Manager phải có role = MANAGER hoặc BRANCH_MANAGER
  - Không được tự quản lý chính mình
  - Hierarchy không được tạo vòng lặp
  
  ### ✅ Ưu điểm:
  - Cấu trúc đơn giản, dễ hiểu
  - Hierarchy tự nhiên với self-join
  - Dễ chuyển đổi vai trò user
  - Query tổ chức đơn giản
  
  ### ⚠️ Nhược điểm:
  - Nhiều nullable fields
  - Logic validation phức tạp
  - Trộn lẫn dữ liệu các role khác nhau
  '''
}
