-- Sale App - User Info Approach (Self-Join Design)
-- Database: PostgreSQL
-- Single table design with self-referencing hierarchy

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing objects if they exist (for clean re-run)
DROP TABLE IF EXISTS user_session CASCADE;
DROP TABLE IF EXISTS registration_history CASCADE;
DROP TABLE IF EXISTS identity_document CASCADE;
DROP TABLE IF EXISTS manager_assignment_history CASCADE;
DROP TABLE IF EXISTS collateral_asset CASCADE;
DROP TABLE IF EXISTS guarantee_history CASCADE;
DROP TABLE IF EXISTS collaborator_registration CASCADE;
DROP TABLE IF EXISTS user_info CASCADE;
DROP TABLE IF EXISTS branch CASCADE;

-- Drop existing types if they exist
DROP TYPE IF EXISTS registration_status CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;
DROP TYPE IF EXISTS user_status CASCADE;
DROP TYPE IF EXISTS branch_status CASCADE;

-- <PERSON><PERSON> <PERSON><PERSON><PERSON> types
CREATE TYPE registration_status AS ENUM (
    'DRAFT',            -- <PERSON><PERSON> soạn thảo
    'PENDING_APPROVAL', -- Chờ duyệt
    'APPROVED',         -- Đã duyệt
    'REJECTED'          -- Bị từ chối
);

CREATE TYPE user_role AS ENUM (
    'COLLABORATOR',     -- Cộng tác viên
    'MANAGER',          -- Quản lý
    'BRANCH_MANAGER',   -- Giám đốc chi nhánh
    'ADMIN'             -- Quản trị viên
);

CREATE TYPE user_status AS ENUM (
    'ACTIVE',           -- Đang hoạt động
    'SUSPENDED',        -- Tạm ngưng
    'TERMINATED',       -- Chấm dứt
    'ON_LEAVE'          -- Nghỉ phép
);

CREATE TYPE branch_status AS ENUM (
    'ACTIVE',           -- Hoạt động
    'INACTIVE'          -- Không hoạt động
);

-- Create tables

-- Branch table
CREATE TABLE branch (
    branch_code VARCHAR(10) PRIMARY KEY,
    branch_name VARCHAR(200) NOT NULL,
    address VARCHAR(500) NOT NULL,
    branch_manager_id VARCHAR(36),
    status branch_status NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Main user_info table with self-join
CREATE TABLE user_info (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36) UNIQUE,
    
    -- Common personal information
    full_name VARCHAR(100) NOT NULL,
    id_card_no VARCHAR(20) NOT NULL UNIQUE,
    issue_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    issue_place VARCHAR(200) NOT NULL,
    permanent_address TEXT NOT NULL,
    phone_number VARCHAR(15) NOT NULL,
    email VARCHAR(100) NOT NULL,
    
    -- Work information
    employee_code VARCHAR(20) NOT NULL UNIQUE,
    role user_role NOT NULL,
    position VARCHAR(100),
    department VARCHAR(100),
    branch_code VARCHAR(10) NOT NULL,
    manager_id VARCHAR(36), -- SELF-JOIN
    
    -- CTV-specific fields (nullable for non-CTV roles)
    core_account_no VARCHAR(20) UNIQUE,
    guarantee_limit DECIMAL(15,2) DEFAULT 0,
    outstanding_balance DECIMAL(15,2) DEFAULT 0,
    collateral_value DECIMAL(15,2) DEFAULT 0,
    
    -- Manager-specific fields (nullable for non-Manager roles)
    max_ctv_limit INTEGER DEFAULT 0,
    current_ctv_count INTEGER DEFAULT 0,
    approval_limit DECIMAL(15,2) DEFAULT 0,
    can_approve_ctv BOOLEAN DEFAULT FALSE,
    can_modify_guarantee BOOLEAN DEFAULT FALSE,
    
    -- Common status and dates
    status user_status NOT NULL DEFAULT 'ACTIVE',
    start_date DATE NOT NULL,
    end_date DATE,
    
    -- Audit fields
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    
    -- Foreign key constraints
    CONSTRAINT fk_user_branch FOREIGN KEY (branch_code) REFERENCES branch(branch_code),
    CONSTRAINT fk_user_manager FOREIGN KEY (manager_id) REFERENCES user_info(id),
    
    -- Business rule constraints
    CONSTRAINT chk_user_not_self_manager CHECK (manager_id != id),
    CONSTRAINT chk_user_expiry_after_issue CHECK (expiry_date > issue_date),
    CONSTRAINT chk_user_outstanding_le_guarantee CHECK (outstanding_balance <= guarantee_limit),
    CONSTRAINT chk_user_current_ctv_le_max CHECK (current_ctv_count <= max_ctv_limit),
    
    -- Role-based constraints
    CONSTRAINT chk_ctv_required_fields CHECK (
        CASE WHEN role = 'COLLABORATOR' 
        THEN core_account_no IS NOT NULL AND registration_id IS NOT NULL
        ELSE TRUE END
    ),
    CONSTRAINT chk_manager_required_fields CHECK (
        CASE WHEN role IN ('MANAGER', 'BRANCH_MANAGER') 
        THEN max_ctv_limit > 0 AND approval_limit >= 0
        ELSE TRUE END
    ),
    CONSTRAINT chk_ctv_no_manager_fields CHECK (
        CASE WHEN role = 'COLLABORATOR'
        THEN max_ctv_limit = 0 AND current_ctv_count = 0 AND approval_limit = 0
        ELSE TRUE END
    )
);

-- Add foreign key for branch manager after user_info is created
ALTER TABLE branch ADD CONSTRAINT fk_branch_manager 
    FOREIGN KEY (branch_manager_id) REFERENCES user_info(id);

-- Registration table
CREATE TABLE collaborator_registration (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Personal information from ID card
    full_name VARCHAR(100) NOT NULL,
    id_card_no VARCHAR(20) NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    issue_place VARCHAR(200) NOT NULL,
    permanent_address TEXT NOT NULL,
    
    -- Contact information
    phone_number VARCHAR(15) NOT NULL,
    internal_email VARCHAR(100),
    
    -- Branch information
    branch_code VARCHAR(10) NOT NULL,
    branch_name VARCHAR(200) NOT NULL,
    referrer_code VARCHAR(36),
    
    -- Status and processing
    status registration_status NOT NULL DEFAULT 'DRAFT',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    submitted_at TIMESTAMP,
    
    created_by VARCHAR(36) NOT NULL,
    approved_by VARCHAR(36),
    approved_at TIMESTAMP,
    rejection_reason TEXT,
    
    CONSTRAINT fk_reg_branch FOREIGN KEY (branch_code) REFERENCES branch(branch_code),
    CONSTRAINT fk_reg_approved_by FOREIGN KEY (approved_by) REFERENCES user_info(id)
);

-- Add foreign key for registration_id after both tables exist
ALTER TABLE user_info ADD CONSTRAINT fk_user_registration 
    FOREIGN KEY (registration_id) REFERENCES collaborator_registration(id);

-- Identity document table
CREATE TABLE identity_document (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36) NOT NULL,
    
    front_image_url VARCHAR(500) NOT NULL,
    back_image_url VARCHAR(500) NOT NULL,
    qr_data TEXT,
    extracted_info JSON,
    
    verification_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    verified_at TIMESTAMP,
    verified_by VARCHAR(36),
    
    CONSTRAINT fk_identity_registration FOREIGN KEY (registration_id) 
        REFERENCES collaborator_registration(id) ON DELETE CASCADE,
    CONSTRAINT fk_identity_verified_by FOREIGN KEY (verified_by) 
        REFERENCES user_info(id),
    CONSTRAINT chk_identity_verification_status 
        CHECK (verification_status IN ('PENDING', 'VERIFIED', 'FAILED'))
);

-- Guarantee history table
CREATE TABLE guarantee_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(36) NOT NULL,
    
    old_guarantee_limit DECIMAL(15,2),
    new_guarantee_limit DECIMAL(15,2) NOT NULL,
    old_outstanding_balance DECIMAL(15,2),
    new_outstanding_balance DECIMAL(15,2) NOT NULL,
    old_collateral_value DECIMAL(15,2),
    new_collateral_value DECIMAL(15,2) NOT NULL,
    
    change_reason TEXT NOT NULL,
    change_type VARCHAR(50) NOT NULL,
    approval_document VARCHAR(200),
    
    effective_date DATE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by VARCHAR(36) NOT NULL,
    approved_by VARCHAR(36),
    approved_at TIMESTAMP,
    
    CONSTRAINT fk_guarantee_user FOREIGN KEY (user_id) REFERENCES user_info(id),
    CONSTRAINT fk_guarantee_created_by FOREIGN KEY (created_by) REFERENCES user_info(id),
    CONSTRAINT fk_guarantee_approved_by FOREIGN KEY (approved_by) REFERENCES user_info(id),
    CONSTRAINT chk_guarantee_change_type 
        CHECK (change_type IN ('INCREASE', 'DECREASE', 'ADJUSTMENT'))
);

-- Collateral asset table
CREATE TABLE collateral_asset (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(36) NOT NULL,
    
    asset_type VARCHAR(50) NOT NULL,
    asset_name VARCHAR(200) NOT NULL,
    asset_address TEXT,
    
    original_value DECIMAL(15,2) NOT NULL,
    appraised_value DECIMAL(15,2) NOT NULL,
    mortgage_value DECIMAL(15,2) NOT NULL,
    appraisal_date DATE NOT NULL,
    appraisal_company VARCHAR(200),
    
    ownership_document VARCHAR(200) NOT NULL,
    document_date DATE NOT NULL,
    document_place VARCHAR(200) NOT NULL,
    
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    mortgage_start_date DATE NOT NULL,
    mortgage_end_date DATE,
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    
    CONSTRAINT fk_collateral_user FOREIGN KEY (user_id) REFERENCES user_info(id),
    CONSTRAINT fk_collateral_created_by FOREIGN KEY (created_by) REFERENCES user_info(id),
    CONSTRAINT fk_collateral_updated_by FOREIGN KEY (updated_by) REFERENCES user_info(id),
    CONSTRAINT chk_collateral_asset_type 
        CHECK (asset_type IN ('REAL_ESTATE', 'VEHICLE', 'DEPOSIT', 'OTHER')),
    CONSTRAINT chk_collateral_status 
        CHECK (status IN ('ACTIVE', 'EXPIRED', 'SUSPENDED', 'CANCELLED')),
    CONSTRAINT chk_collateral_values
        CHECK (mortgage_value <= appraised_value AND appraised_value > 0)
);

-- Manager assignment history table
CREATE TABLE manager_assignment_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(36) NOT NULL,

    old_manager_id VARCHAR(36),
    new_manager_id VARCHAR(36) NOT NULL,

    assignment_reason TEXT NOT NULL,
    assignment_type VARCHAR(50) NOT NULL,
    approval_document VARCHAR(200),

    effective_date DATE NOT NULL,
    end_date DATE,

    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by VARCHAR(36) NOT NULL,
    approved_by VARCHAR(36),
    approved_at TIMESTAMP,

    CONSTRAINT fk_assignment_user FOREIGN KEY (user_id) REFERENCES user_info(id),
    CONSTRAINT fk_assignment_old_manager FOREIGN KEY (old_manager_id) REFERENCES user_info(id),
    CONSTRAINT fk_assignment_new_manager FOREIGN KEY (new_manager_id) REFERENCES user_info(id),
    CONSTRAINT fk_assignment_created_by FOREIGN KEY (created_by) REFERENCES user_info(id),
    CONSTRAINT fk_assignment_approved_by FOREIGN KEY (approved_by) REFERENCES user_info(id),
    CONSTRAINT chk_assignment_type
        CHECK (assignment_type IN ('INITIAL', 'TRANSFER', 'REPLACEMENT'))
);

-- Registration history table
CREATE TABLE registration_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id VARCHAR(36) NOT NULL,

    action VARCHAR(50) NOT NULL,
    old_status registration_status,
    new_status registration_status NOT NULL,
    notes TEXT,

    performed_by VARCHAR(36) NOT NULL,
    performed_at TIMESTAMP NOT NULL DEFAULT NOW(),

    CONSTRAINT fk_reg_history_registration FOREIGN KEY (registration_id)
        REFERENCES collaborator_registration(id),
    CONSTRAINT fk_reg_history_performed_by FOREIGN KEY (performed_by)
        REFERENCES user_info(id),
    CONSTRAINT chk_reg_history_action
        CHECK (action IN ('CREATED', 'UPDATED', 'SUBMITTED', 'APPROVED', 'REJECTED'))
);

-- User session table
CREATE TABLE user_session (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(36),
    registration_id VARCHAR(36),

    device_id VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,

    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',

    CONSTRAINT fk_session_user FOREIGN KEY (user_id) REFERENCES user_info(id),
    CONSTRAINT fk_session_registration FOREIGN KEY (registration_id)
        REFERENCES collaborator_registration(id),
    CONSTRAINT chk_session_status CHECK (status IN ('ACTIVE', 'EXPIRED')),
    CONSTRAINT chk_session_user_or_registration
        CHECK ((user_id IS NOT NULL) OR (registration_id IS NOT NULL))
);

-- Create indexes for performance
CREATE INDEX idx_user_employee_code ON user_info(employee_code);
CREATE INDEX idx_user_id_card ON user_info(id_card_no);
CREATE INDEX idx_user_core_account ON user_info(core_account_no);
CREATE INDEX idx_user_role ON user_info(role);
CREATE INDEX idx_user_branch ON user_info(branch_code);
CREATE INDEX idx_user_manager ON user_info(manager_id);
CREATE INDEX idx_user_status ON user_info(status);
CREATE INDEX idx_user_registration ON user_info(registration_id);

CREATE INDEX idx_reg_status ON collaborator_registration(status);
CREATE INDEX idx_reg_branch ON collaborator_registration(branch_code);
CREATE INDEX idx_reg_created_at ON collaborator_registration(created_at);

CREATE INDEX idx_guarantee_user ON guarantee_history(user_id);
CREATE INDEX idx_guarantee_effective ON guarantee_history(effective_date);
CREATE INDEX idx_guarantee_change_type ON guarantee_history(change_type);

CREATE INDEX idx_collateral_user ON collateral_asset(user_id);
CREATE INDEX idx_collateral_type ON collateral_asset(asset_type);
CREATE INDEX idx_collateral_status ON collateral_asset(status);

CREATE INDEX idx_assignment_user ON manager_assignment_history(user_id);
CREATE INDEX idx_assignment_new_manager ON manager_assignment_history(new_manager_id);
CREATE INDEX idx_assignment_effective ON manager_assignment_history(effective_date);

CREATE INDEX idx_session_user ON user_session(user_id);
CREATE INDEX idx_session_device ON user_session(device_id);
CREATE INDEX idx_session_status ON user_session(status);

-- Create triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_branch_updated_at BEFORE UPDATE ON branch
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_info_updated_at BEFORE UPDATE ON user_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_registration_updated_at BEFORE UPDATE ON collaborator_registration
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_collateral_updated_at BEFORE UPDATE ON collateral_asset
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Business logic functions
CREATE OR REPLACE FUNCTION update_ctv_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update current_ctv_count when manager_id changes
    IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.manager_id != NEW.manager_id) THEN
        -- Decrease count for old manager
        IF TG_OP = 'UPDATE' AND OLD.manager_id IS NOT NULL THEN
            UPDATE user_info
            SET current_ctv_count = current_ctv_count - 1
            WHERE id = OLD.manager_id AND role IN ('MANAGER', 'BRANCH_MANAGER');
        END IF;

        -- Increase count for new manager
        IF NEW.manager_id IS NOT NULL AND NEW.role = 'COLLABORATOR' THEN
            UPDATE user_info
            SET current_ctv_count = current_ctv_count + 1
            WHERE id = NEW.manager_id AND role IN ('MANAGER', 'BRANCH_MANAGER');
        END IF;
    END IF;

    -- Decrease count when CTV is deleted or becomes inactive
    IF TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND NEW.status != 'ACTIVE' AND OLD.status = 'ACTIVE') THEN
        IF COALESCE(OLD.manager_id, NEW.manager_id) IS NOT NULL THEN
            UPDATE user_info
            SET current_ctv_count = current_ctv_count - 1
            WHERE id = COALESCE(OLD.manager_id, NEW.manager_id)
              AND role IN ('MANAGER', 'BRANCH_MANAGER');
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_ctv_count
    AFTER INSERT OR UPDATE OR DELETE ON user_info
    FOR EACH ROW EXECUTE FUNCTION update_ctv_count();

-- Function to auto-create user_info from approved registration
CREATE OR REPLACE FUNCTION create_user_from_registration()
RETURNS TRIGGER AS $$
BEGIN
    -- When registration is approved, create user_info record
    IF NEW.status = 'APPROVED' AND OLD.status = 'PENDING_APPROVAL' THEN
        INSERT INTO user_info (
            registration_id, full_name, id_card_no, issue_date, expiry_date,
            issue_place, permanent_address, phone_number, email,
            employee_code, role, branch_code, status, start_date, created_by
        ) VALUES (
            NEW.id, NEW.full_name, NEW.id_card_no, NEW.issue_date, NEW.expiry_date,
            NEW.issue_place, NEW.permanent_address, NEW.phone_number,
            COALESCE(NEW.internal_email, NEW.phone_number || '@temp.com'),
            'CTV' || LPAD(nextval('user_employee_seq')::text, 6, '0'),
            'COLLABORATOR', NEW.branch_code, 'ACTIVE', CURRENT_DATE, NEW.approved_by
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sequence for employee codes
CREATE SEQUENCE user_employee_seq START 1000;

CREATE TRIGGER trg_create_user_from_registration
    AFTER UPDATE ON collaborator_registration
    FOR EACH ROW EXECUTE FUNCTION create_user_from_registration();

-- Insert sample data
INSERT INTO branch (branch_code, branch_name, address) VALUES
('HN001', 'Chi nhánh Hà Nội 1', '123 Đường ABC, Ba Đình, Hà Nội'),
('HCM001', 'Chi nhánh TP.HCM 1', '456 Đường XYZ, Quận 1, TP.HCM'),
('DN001', 'Chi nhánh Đà Nẵng 1', '789 Đường DEF, Hải Châu, Đà Nẵng');

-- Insert sample admin user
INSERT INTO user_info (
    full_name, id_card_no, issue_date, expiry_date, issue_place, permanent_address,
    phone_number, email, employee_code, role, branch_code, status, start_date, created_by
) VALUES (
    'Nguyễn Văn Admin', '001234567890', '2020-01-01', '2030-01-01', 'CA Hà Nội',
    '123 Admin Street, Hà Nội', '0901234567', '<EMAIL>',
    'ADMIN001', 'ADMIN', 'HN001', 'ACTIVE', '2024-01-01', uuid_generate_v4()
);

-- Insert sample branch manager
INSERT INTO user_info (
    full_name, id_card_no, issue_date, expiry_date, issue_place, permanent_address,
    phone_number, email, employee_code, role, position, branch_code,
    max_ctv_limit, approval_limit, can_approve_ctv, can_modify_guarantee,
    status, start_date, created_by
) VALUES (
    'Trần Thị Giám Đốc', '001234567891', '2020-01-01', '2030-01-01', 'CA Hà Nội',
    '456 Manager Street, Hà Nội', '0901234568', '<EMAIL>',
    'MGR001', 'BRANCH_MANAGER', 'Giám đốc chi nhánh', 'HN001',
    50, 5000000000, TRUE, TRUE, 'ACTIVE', '2024-01-01',
    (SELECT id FROM user_info WHERE employee_code = 'ADMIN001')
);

-- Update branch manager reference
UPDATE branch SET branch_manager_id = (SELECT id FROM user_info WHERE employee_code = 'MGR001')
WHERE branch_code = 'HN001';

/*
BUSINESS RULES IMPLEMENTATION SUMMARY:

1. ✅ Self-Join Hierarchy: manager_id → user_info.id
2. ✅ Role-based Field Validation via CHECK constraints
3. ✅ Automatic CTV count management via triggers
4. ✅ Auto-creation of user_info from approved registrations
5. ✅ Comprehensive audit trail
6. ✅ Financial constraints validation
7. ✅ Session management for security
8. ✅ Indexes for optimal performance
9. ✅ Sample data for testing

Key Features:
- Single table design with self-referencing hierarchy
- Role-based nullable fields with validation
- Automatic business logic via triggers
- Complete audit trail for all changes
- Optimized for banking domain requirements
*/
