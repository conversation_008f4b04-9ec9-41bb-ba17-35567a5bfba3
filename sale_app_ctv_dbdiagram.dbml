// Sale App - CTV Registration Database Schema
// Optimized for dbdiagram.io
// Copy and paste this entire content to https://dbdiagram.io/

Project SaleApp_CTV {
  database_type: 'PostgreSQL'
  Note: '''
    # Sale App - CTV Registration System
    
    **Features:**
    - Identity document verification via QR scanning
    - Multi-status registration workflow  
    - Audit trail and session management
    - Business rule enforcement
    
    **Status Flow:** DRAFT → PENDING_APPROVAL → APPROVED/REJECTED
  '''
}

// ===== ENUM DEFINITIONS =====
Enum registration_status {
  DRAFT [note: '🟡 Đang soạn thảo - có thể làm lại']
  PENDING_APPROVAL [note: '🟠 Chờ duyệt - không được làm lại']
  APPROVED [note: '🟢 Đã duyệt']
  REJECTED [note: '🔴 Bị từ chối']
}

Enum branch_status {
  ACTIVE [note: '✅ Hoạt động']
  INACTIVE [note: '❌ Không hoạt động']
}

// ===== CORE TABLES =====

Table branch {
  branch_code varchar(10) [pk, note: '🏢 Mã chi nhánh']
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  address varchar(500) [not null, note: 'Địa chỉ chi nhánh']
  manager_id varchar(36) [note: 'ID quản lý chi nhánh']
  status branch_status [not null, default: 'ACTIVE']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  Note: 'Bảng lưu thông tin chi nhánh'
}

Table collaborator_registration {
  id varchar(36) [pk, note: '📋 UUID đăng ký CTV']
  
  // Thông tin từ GTTT
  full_name varchar(100) [not null, note: 'Tên đầy đủ từ GTTT']
  id_card_no varchar(20) [not null, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  front_card_url varchar(100) [not null, note: 'Link ảnh mặt trước GTTT']
  back_card_url varchar(100) [not null, note: 'Link ảnh mặt sau GTTT']
  
  // Thông tin KH tự nhập
  phone_number varchar(15) [not null, note: 'Số điện thoại liên hệ']
  internal_email varchar(100) [note: 'Email nội bộ (tùy chọn)']
  
  // Thông tin chi nhánh và giới thiệu
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Mã chi nhánh']
  referrer_code varchar(36) [note: 'Người giới thiệu (tùy chọn)']
  
  // Trạng thái và thời gian
  status registration_status [not null, default: 'DRAFT']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']

  // Thông tin nguồn tạo dữ liệu.
  data_source varchar(36) [not null, note: 'Nguồn tạo dữ liệu (AppSale/WebSale/KPlus)']
  
  // Thông tin xử lý
  created_by varchar(36) [not null, note: 'User tạo đăng ký']
  approved_by varchar(36) [note: 'User duyệt đăng ký']
  approved_at timestamp [note: 'Thời gian duyệt']
  rejection_reason text [note: 'Lý do từ chối (nếu có)']
  
  Note: '''
    📋 **Bảng chính đăng ký CTV**
    
    Lưu trữ toàn bộ thông tin đăng ký cộng tác viên
    bao gồm thông tin từ GTTT và thông tin bổ sung
  '''
}

// ===== BUSINESS RULES =====
Note business_rules {
  '''
  ## 📋 QUY TẮC NGHIỆP VỤ
  
  ### 🔄 Luồng trạng thái:
  1. **DRAFT** → Có thể chỉnh sửa và làm lại từ đầu
  2. **PENDING_APPROVAL** → Chờ duyệt, không được sửa đổi
  3. **APPROVED/REJECTED** → Kết thúc luồng
  
  ### ✅ Ràng buộc:
  - Mỗi đăng ký chỉ có 1 identity_document
  - Bắt buộc có GTTT trước khi submit
  - referrer_id và internal_email là tùy chọn
  - Audit trail đầy đủ qua registration_history
  
  ### 🔐 Bảo mật:
  - Session tracking cho mỗi thiết bị
  - Validation dữ liệu đầu vào
  - Timestamp tự động cập nhật
  '''
}