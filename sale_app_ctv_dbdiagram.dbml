// Sale App - CTV Registration Database Schema
// Optimized for dbdiagram.io
// Copy and paste this entire content to https://dbdiagram.io/

Project SaleApp_CTV {
  database_type: 'PostgreSQL'
  Note: '''
    # Sale App - CTV Registration System
    
    **Features:**
    - Identity document verification via QR scanning
    - Multi-status registration workflow  
    - Audit trail and session management
    - Business rule enforcement
    
    **Status Flow:** DRAFT → PENDING_APPROVAL → APPROVED/REJECTED
  '''
}

// ===== ENUM DEFINITIONS =====
Enum registration_status {
  DRAFT [note: '🟡 Đang soạn thảo - có thể làm lại']
  PENDING_APPROVAL [note: '🟠 Chờ duyệt - không được làm lại']
  APPROVED [note: '🟢 Đã duyệt']
  REJECTED [note: '🔴 Bị từ chối']
}

Enum branch_status {
  ACTIVE [note: '✅ Hoạt động']
  INACTIVE [note: '❌ Không hoạt động']
}

Enum collaborator_status {
  ACTIVE [note: '🟢 Đang hoạt động']
  SUSPENDED [note: '🟡 Tạm ngưng']
  TERMINATED [note: '🔴 Chấm dứt hợp tác']
  ON_LEAVE [note: '🟠 Nghỉ phép']
}

Enum guarantee_status {
  ACTIVE [note: '✅ Đang hiệu lực']
  EXPIRED [note: '⏰ Đã hết hạn']
  SUSPENDED [note: '⏸️ Tạm ngưng']
  CANCELLED [note: '❌ Đã hủy']
}

Enum manager_status {
  ACTIVE [note: '✅ Đang hoạt động']
  INACTIVE [note: '❌ Không hoạt động']
  TRANSFERRED [note: '🔄 Đã chuyển công tác']
}

// ===== CORE TABLES =====

Table branch {
  branch_code varchar(10) [pk, note: '🏢 Mã chi nhánh']
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  address varchar(500) [not null, note: 'Địa chỉ chi nhánh']
  branch_manager_id varchar(36) [note: 'ID giám đốc chi nhánh']
  status branch_status [not null, default: 'ACTIVE']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  Note: 'Bảng lưu thông tin chi nhánh'
}

Table manager {
  id varchar(36) [pk, note: '👨‍💼 UUID quản lý']

  // Thông tin cá nhân
  full_name varchar(100) [not null, note: 'Họ tên quản lý']
  employee_code varchar(20) [not null, unique, note: 'Mã nhân viên quản lý']
  id_card_no varchar(20) [not null, note: 'Số GTTT']
  phone_number varchar(15) [not null, note: 'Số điện thoại']
  email varchar(100) [not null, note: 'Email công ty']

  // Thông tin công việc
  position varchar(100) [not null, note: 'Chức vụ (Trưởng phòng, Phó giám đốc, ...)']
  department varchar(100) [note: 'Phòng ban']
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Chi nhánh làm việc']
  direct_manager_id varchar(36) [ref: > manager.id, note: 'Quản lý cấp trên']

  // Thông tin quản lý CTV
  max_ctv_limit int [not null, default: 0, note: 'Số lượng CTV tối đa được quản lý']
  current_ctv_count int [not null, default: 0, note: 'Số lượng CTV hiện tại']
  total_guarantee_limit decimal(15,2) [not null, default: 0, note: 'Tổng hạn mức được phê duyệt']

  // Thông tin ủy quyền
  approval_limit decimal(15,2) [not null, default: 0, note: 'Hạn mức phê duyệt tối đa']
  can_approve_ctv boolean [not null, default: false, note: 'Có quyền duyệt đăng ký CTV']
  can_modify_guarantee boolean [not null, default: false, note: 'Có quyền điều chỉnh hạn mức']

  // Trạng thái
  status manager_status [not null, default: 'ACTIVE']
  start_date date [not null, note: 'Ngày bắt đầu làm việc']
  end_date date [note: 'Ngày kết thúc (nếu có)']

  // Audit fields
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null]
  updated_by varchar(36)

  indexes {
    employee_code [name: 'idx_manager_employee_code']
    branch_code [name: 'idx_manager_branch']
    direct_manager_id [name: 'idx_manager_direct_manager']
    status [name: 'idx_manager_status']
  }

  Note: '''
    👨‍💼 **Thông tin quản lý**

    Lưu trữ thông tin chi tiết về các quản lý
    có thể quản lý CTV, bao gồm thông tin cá nhân,
    chức vụ, quyền hạn và giới hạn phê duyệt.
  '''
}

Table collaborator_registration {
  id varchar(36) [pk, note: '📋 UUID đăng ký CTV']
  
  // Thông tin từ GTTT
  full_name varchar(100) [not null, note: 'Tên đầy đủ từ GTTT']
  id_card_no varchar(20) [not null, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  front_card_url varchar(100) [not null, note: 'Link ảnh mặt trước GTTT']
  back_card_url varchar(100) [not null, note: 'Link ảnh mặt sau GTTT']
  
  // Thông tin KH tự nhập
  phone_number varchar(15) [not null, note: 'Số điện thoại liên hệ']
  internal_email varchar(100) [note: 'Email nội bộ (tùy chọn)']
  
  // Thông tin chi nhánh và giới thiệu
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Mã chi nhánh']
  referrer_code varchar(36) [note: 'Người giới thiệu (tùy chọn)']
  
  // Trạng thái và thời gian
  status registration_status [not null, default: 'DRAFT']
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']

  // Thông tin nguồn tạo dữ liệu.
  data_source varchar(36) [not null, note: 'Nguồn tạo dữ liệu (AppSale/WebSale/KPlus)']
  
  // Thông tin xử lý
  created_by varchar(36) [not null, note: 'User tạo đăng ký']
  approved_by varchar(36) [note: 'User duyệt đăng ký']
  approved_at timestamp [note: 'Thời gian duyệt']
  rejection_reason text [note: 'Lý do từ chối (nếu có)']
  
  Note: '''
    📋 **Bảng chính đăng ký CTV**

    Lưu trữ toàn bộ thông tin đăng ký cộng tác viên
    bao gồm thông tin từ GTTT và thông tin bổ sung
  '''
}

Table collaborator {
  id varchar(36) [pk, note: '👤 UUID cộng tác viên']
  registration_id varchar(36) [not null, unique, ref: - collaborator_registration.id, note: 'Liên kết với đăng ký']

  // Thông tin cá nhân (copy từ registration)
  full_name varchar(100) [not null, note: 'Tên đầy đủ']
  id_card_no varchar(20) [not null, unique, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  phone_number varchar(15) [not null, note: 'Số điện thoại']
  internal_email varchar(100) [note: 'Email nội bộ']

  // Thông tin ngân hàng
  employee_code varchar(20) [not null, unique, note: '🏦 Mã nhân viên ngân hàng']
  core_account_no varchar(20) [not null, unique, note: '💳 Số tài khoản core (thu nợ đầu ngày)']
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Chi nhánh làm việc']
  manager_id varchar(36) [not null, ref: > manager.id, note: '👨‍💼 ID người quản lý trực tiếp']

  // Thông tin hạn mức và tài sản
  guarantee_limit decimal(15,2) [not null, default: 0, note: '💰 Hạn mức bảo lãnh']
  outstanding_balance decimal(15,2) [not null, default: 0, note: '📊 Dư nợ còn lại']
  collateral_value decimal(15,2) [not null, default: 0, note: '🏠 Giá trị tài sản thế chấp']

  // Trạng thái và thời gian
  status collaborator_status [not null, default: 'ACTIVE', note: 'Trạng thái CTV']
  start_date date [not null, note: 'Ngày bắt đầu hợp tác']
  end_date date [note: 'Ngày kết thúc hợp tác']

  // Audit fields
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null, note: 'User tạo hồ sơ CTV']
  updated_by varchar(36) [note: 'User cập nhật cuối']

  indexes {
    employee_code [name: 'idx_ctv_employee_code']
    core_account_no [name: 'idx_ctv_core_account']
    id_card_no [name: 'idx_ctv_id_card']
    branch_code [name: 'idx_ctv_branch']
    status [name: 'idx_ctv_status']
    manager_id [name: 'idx_ctv_manager']
  }

  Note: '''
    👤 **Thông tin cộng tác viên**

    Lưu trữ thông tin CTV sau khi được duyệt và chính thức
    hoạt động tại ngân hàng. Bao gồm thông tin cá nhân,
    thông tin ngân hàng, hạn mức và tài sản thế chấp.
  '''
}

Table guarantee_history {
  id varchar(36) [pk, note: '📈 UUID lịch sử hạn mức']
  collaborator_id varchar(36) [not null, ref: > collaborator.id, note: 'ID cộng tác viên']

  // Thông tin hạn mức
  old_guarantee_limit decimal(15,2) [note: 'Hạn mức cũ']
  new_guarantee_limit decimal(15,2) [not null, note: 'Hạn mức mới']
  old_outstanding_balance decimal(15,2) [note: 'Dư nợ cũ']
  new_outstanding_balance decimal(15,2) [not null, note: 'Dư nợ mới']
  old_collateral_value decimal(15,2) [note: 'Giá trị tài sản cũ']
  new_collateral_value decimal(15,2) [not null, note: 'Giá trị tài sản mới']

  // Thông tin thay đổi
  change_reason text [not null, note: 'Lý do thay đổi']
  change_type varchar(50) [not null, note: 'Loại thay đổi (INCREASE/DECREASE/ADJUSTMENT)']
  approval_document varchar(200) [note: 'Số văn bản phê duyệt']

  // Audit fields
  effective_date date [not null, note: 'Ngày hiệu lực']
  created_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null, note: 'User thực hiện thay đổi']
  approved_by varchar(36) [note: 'User phê duyệt thay đổi']
  approved_at timestamp [note: 'Thời gian phê duyệt']

  indexes {
    collaborator_id [name: 'idx_guarantee_collaborator']
    effective_date [name: 'idx_guarantee_effective']
    change_type [name: 'idx_guarantee_change_type']
    created_at [name: 'idx_guarantee_created']
  }

  Note: '''
    📈 **Lịch sử hạn mức bảo lãnh**

    Theo dõi mọi thay đổi về hạn mức bảo lãnh,
    dư nợ và giá trị tài sản thế chấp của CTV
  '''
}

Table manager_assignment_history {
  id varchar(36) [pk, note: '📋 UUID lịch sử phân công']
  collaborator_id varchar(36) [not null, ref: > collaborator.id, note: 'ID cộng tác viên']

  // Thông tin thay đổi quản lý
  old_manager_id varchar(36) [ref: > manager.id, note: 'Quản lý cũ']
  new_manager_id varchar(36) [not null, ref: > manager.id, note: 'Quản lý mới']

  // Thông tin thay đổi
  assignment_reason text [not null, note: 'Lý do thay đổi quản lý']
  assignment_type varchar(50) [not null, note: 'Loại thay đổi (INITIAL/TRANSFER/REPLACEMENT)']
  approval_document varchar(200) [note: 'Số văn bản phê duyệt']

  // Thời gian hiệu lực
  effective_date date [not null, note: 'Ngày hiệu lực']
  end_date date [note: 'Ngày kết thúc (nếu có)']

  // Audit fields
  created_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null, note: 'User thực hiện phân công']
  approved_by varchar(36) [note: 'User phê duyệt']
  approved_at timestamp [note: 'Thời gian phê duyệt']

  indexes {
    collaborator_id [name: 'idx_assignment_collaborator']
    old_manager_id [name: 'idx_assignment_old_manager']
    new_manager_id [name: 'idx_assignment_new_manager']
    effective_date [name: 'idx_assignment_effective']
    assignment_type [name: 'idx_assignment_type']
  }

  Note: '''
    📋 **Lịch sử phân công quản lý**

    Theo dõi mọi thay đổi về việc phân công
    quản lý cho từng CTV, bao gồm lý do và
    thời gian hiệu lực.
  '''
}

Table collateral_asset {
  id varchar(36) [pk, note: '🏠 UUID tài sản thế chấp']
  collaborator_id varchar(36) [not null, ref: > collaborator.id, note: 'ID cộng tác viên']

  // Thông tin tài sản
  asset_type varchar(50) [not null, note: 'Loại tài sản (REAL_ESTATE/VEHICLE/DEPOSIT/OTHER)']
  asset_name varchar(200) [not null, note: 'Tên/mô tả tài sản']
  asset_address text [note: 'Địa chỉ tài sản (nếu có)']

  // Giá trị và thẩm định
  original_value decimal(15,2) [not null, note: 'Giá trị gốc']
  appraised_value decimal(15,2) [not null, note: 'Giá trị thẩm định']
  mortgage_value decimal(15,2) [not null, note: 'Giá trị thế chấp']
  appraisal_date date [not null, note: 'Ngày thẩm định']
  appraisal_company varchar(200) [note: 'Công ty thẩm định']

  // Giấy tờ pháp lý
  ownership_document varchar(200) [not null, note: 'Số giấy chứng nhận quyền sở hữu']
  document_date date [not null, note: 'Ngày cấp giấy tờ']
  document_place varchar(200) [not null, note: 'Nơi cấp giấy tờ']

  // Trạng thái
  status guarantee_status [not null, default: 'ACTIVE', note: 'Trạng thái tài sản']
  mortgage_start_date date [not null, note: 'Ngày bắt đầu thế chấp']
  mortgage_end_date date [note: 'Ngày kết thúc thế chấp']

  // Audit fields
  created_at timestamp [not null, default: 'now()']
  updated_at timestamp [not null, default: 'now()']
  created_by varchar(36) [not null]
  updated_by varchar(36)

  indexes {
    collaborator_id [name: 'idx_collateral_collaborator']
    asset_type [name: 'idx_collateral_type']
    status [name: 'idx_collateral_status']
    ownership_document [name: 'idx_collateral_document']
  }

  Note: '''
    🏠 **Tài sản thế chấp**

    Chi tiết các tài sản được sử dụng làm
    thế chấp cho hạn mức bảo lãnh của CTV
  '''
}

// ===== BUSINESS RULES =====
Note business_rules {
  '''
  ## 📋 QUY TẮC NGHIỆP VỤ

  ### 🔄 Luồng đăng ký CTV:
  1. **DRAFT** → Có thể chỉnh sửa và làm lại từ đầu
  2. **PENDING_APPROVAL** → Chờ duyệt, không được sửa đổi
  3. **APPROVED** → Tạo hồ sơ CTV trong bảng collaborator
  4. **REJECTED** → Kết thúc luồng

  ### 🏦 Luồng quản lý CTV:
  1. **ACTIVE** → CTV đang hoạt động bình thường
  2. **SUSPENDED** → Tạm ngưng do vi phạm hoặc yêu cầu
  3. **ON_LEAVE** → Nghỉ phép có thời hạn
  4. **TERMINATED** → Chấm dứt hợp tác vĩnh viễn

  ### ✅ Ràng buộc đăng ký:
  - Mỗi đăng ký chỉ có 1 identity_document
  - Bắt buộc có GTTT trước khi submit
  - referrer_code và internal_email là tùy chọn
  - Audit trail đầy đủ qua registration_history

  ### 💰 Ràng buộc hạn mức:
  - outstanding_balance ≤ guarantee_limit
  - collateral_value ≥ guarantee_limit * hệ số an toàn
  - Mọi thay đổi hạn mức phải có approval
  - Lịch sử thay đổi được lưu đầy đủ

  ### 🏠 Ràng buộc tài sản thế chấp:
  - Tổng mortgage_value ≥ guarantee_limit
  - Tài sản phải có giấy tờ pháp lý hợp lệ
  - Thẩm định giá định kỳ theo quy định
  - Trạng thái tài sản phải ACTIVE để tính vào hạn mức

  ### 👨‍💼 Ràng buộc quản lý:
  - Mỗi CTV phải có 1 quản lý trực tiếp
  - current_ctv_count ≤ max_ctv_limit cho mỗi manager
  - Quản lý phải thuộc cùng chi nhánh hoặc cấp trên
  - Thay đổi quản lý phải có lý do và phê duyệt
  - Lịch sử phân công được lưu đầy đủ

  ### 🔐 Phân quyền và kiểm soát:
  - can_approve_ctv: Quyền duyệt đăng ký CTV mới
  - can_modify_guarantee: Quyền điều chỉnh hạn mức
  - approval_limit: Giới hạn hạn mức có thể phê duyệt
  - Phân quyền theo vai trò và chi nhánh
  - Session tracking cho mỗi thiết bị
  - Validation dữ liệu đầu vào
  - Timestamp tự động cập nhật
  - Audit trail đầy đủ cho mọi thay đổi quan trọng
  '''
}