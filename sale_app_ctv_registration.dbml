// Database Schema for Sale App - CTV Registration
// Generated for Collaborator Registration Flow

Project SaleApp_CTV {
  database_type: 'PostgreSQL'
  Note: '''
    Database schema for Sale App CTV (Collaborator) Registration System
    Supports complete registration flow with identity document verification
  '''
}

// Enum definitions
Enum registration_status {
  DRAFT [note: 'Đang soạn thảo - có thể làm lại từ đầu']
  PENDING_APPROVAL [note: 'Chờ duyệt - không được làm lại']
  APPROVED [note: 'Đã duyệt']
  REJECTED [note: 'Bị từ chối']
}

Enum verification_status {
  PENDING [note: 'Chờ xác thực']
  VERIFIED [note: 'Đã xác thực']
  FAILED [note: 'Xác thực thất bại']
}

Enum branch_status {
  ACTIVE [note: 'Hoạt động']
  INACTIVE [note: 'Không hoạt động']
}

Enum referrer_status {
  ACTIVE [note: 'Hoạt động']
  INACTIVE [note: 'Không hoạt động']
}

Enum history_action {
  CREATED [note: 'Tạo mới']
  UPDATED [note: 'Cập nhật']
  SUBMITTED [note: 'Nộp đăng ký']
  APPROVED [note: 'Duyệt']
  REJECTED [note: 'Từ chối']
}

Enum session_status {
  ACTIVE [note: 'Đang hoạt động']
  EXPIRED [note: 'Đã hết hạn']
}

// Main tables
Table collaborator_registration {
  id varchar(36) [pk, note: 'UUID primary key']
  full_name varchar(100) [not null, note: 'Tên đầy đủ từ GTTT']
  id_number varchar(20) [not null, note: 'Số GTTT']
  issue_date date [not null, note: 'Ngày cấp GTTT']
  expiry_date date [not null, note: 'Ngày hết hạn GTTT']
  issue_place varchar(200) [not null, note: 'Nơi cấp GTTT']
  permanent_address text [not null, note: 'Địa chỉ thường trú']
  phone_number varchar(15) [not null, note: 'Số điện thoại liên hệ']
  internal_email varchar(100) [null, note: 'Email nội bộ (tùy chọn)']
  branch_code varchar(10) [not null, note: 'Mã chi nhánh']
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  referrer_id varchar(36) [null, note: 'ID người giới thiệu (tùy chọn)']
  registration_status registration_status [not null, default: 'DRAFT', note: 'Trạng thái đăng ký']
  created_at timestamp [not null, default: `now()`, note: 'Thời gian tạo']
  updated_at timestamp [not null, default: `now()`, note: 'Thời gian cập nhật']
  submitted_at timestamp [null, note: 'Thời gian submit đăng ký']
  created_by varchar(36) [not null, note: 'User tạo đăng ký']
  approved_by varchar(36) [null, note: 'User duyệt đăng ký']
  approved_at timestamp [null, note: 'Thời gian duyệt']
  rejection_reason text [null, note: 'Lý do từ chối (nếu có)']
  
  indexes {
    id_number [name: 'idx_ctv_id_number']
    phone_number [name: 'idx_ctv_phone']
    registration_status [name: 'idx_ctv_status']
    branch_code [name: 'idx_ctv_branch']
    created_at [name: 'idx_ctv_created']
  }
  
  Note: 'Bảng chính lưu thông tin đăng ký CTV'
}

Table identity_document {
  id varchar(36) [pk, note: 'UUID primary key']
  registration_id varchar(36) [not null, ref: > collaborator_registration.id, note: 'ID đăng ký CTV']
  front_image_url varchar(500) [not null, note: 'URL ảnh mặt trước GTTT']
  back_image_url varchar(500) [not null, note: 'URL ảnh mặt sau GTTT']
  qr_data text [null, note: 'Dữ liệu QR code đã quét']
  extracted_info json [null, note: 'Thông tin trích xuất từ QR']
  verification_status verification_status [not null, default: 'PENDING', note: 'Trạng thái xác thực']
  created_at timestamp [not null, default: `now()`, note: 'Thời gian tạo']
  verified_at timestamp [null, note: 'Thời gian xác thực']
  
  indexes {
    registration_id [name: 'idx_identity_registration']
    verification_status [name: 'idx_identity_status']
  }
  
  Note: 'Bảng lưu thông tin giấy tờ tùy thân'
}

Table branch {
  branch_code varchar(10) [pk, note: 'Mã chi nhánh']
  branch_name varchar(200) [not null, note: 'Tên chi nhánh']
  address varchar(500) [not null, note: 'Địa chỉ chi nhánh']
  phone varchar(15) [not null, note: 'Số điện thoại chi nhánh']
  email varchar(100) [not null, note: 'Email chi nhánh']
  manager_id varchar(36) [null, note: 'ID quản lý chi nhánh']
  status branch_status [not null, default: 'ACTIVE', note: 'Trạng thái chi nhánh']
  created_at timestamp [not null, default: `now()`, note: 'Thời gian tạo']
  updated_at timestamp [not null, default: `now()`, note: 'Thời gian cập nhật']
  
  indexes {
    branch_name [name: 'idx_branch_name']
    status [name: 'idx_branch_status']
  }
  
  Note: 'Master data chi nhánh'
}

Table referrer {
  id varchar(36) [pk, note: 'UUID primary key']
  full_name varchar(100) [not null, note: 'Tên người giới thiệu']
  employee_code varchar(20) [not null, unique, note: 'Mã nhân viên']
  phone_number varchar(15) [not null, note: 'Số điện thoại']
  email varchar(100) [not null, note: 'Email']
  branch_code varchar(10) [not null, ref: > branch.branch_code, note: 'Mã chi nhánh']
  status referrer_status [not null, default: 'ACTIVE', note: 'Trạng thái']
  created_at timestamp [not null, default: `now()`, note: 'Thời gian tạo']
  updated_at timestamp [not null, default: `now()`, note: 'Thời gian cập nhật']
  
  indexes {
    employee_code [name: 'idx_referrer_code']
    branch_code [name: 'idx_referrer_branch']
    status [name: 'idx_referrer_status']
  }
  
  Note: 'Bảng thông tin người giới thiệu'
}

Table registration_history {
  id varchar(36) [pk, note: 'UUID primary key']
  registration_id varchar(36) [not null, ref: > collaborator_registration.id, note: 'ID đăng ký']
  action history_action [not null, note: 'Hành động thực hiện']
  old_status registration_status [null, note: 'Trạng thái cũ']
  new_status registration_status [not null, note: 'Trạng thái mới']
  notes text [null, note: 'Ghi chú']
  performed_by varchar(36) [not null, note: 'Người thực hiện']
  performed_at timestamp [not null, default: `now()`, note: 'Thời gian thực hiện']
  
  indexes {
    registration_id [name: 'idx_history_registration']
    action [name: 'idx_history_action']
    performed_at [name: 'idx_history_time']
  }
  
  Note: 'Bảng lịch sử thay đổi trạng thái đăng ký'
}

Table user_session {
  id varchar(36) [pk, note: 'UUID primary key']
  registration_id varchar(36) [null, ref: > collaborator_registration.id, note: 'ID đăng ký (nếu có)']
  device_id varchar(100) [not null, note: 'ID thiết bị']
  ip_address varchar(45) [not null, note: 'Địa chỉ IP']
  user_agent text [not null, note: 'Thông tin trình duyệt']
  created_at timestamp [not null, default: `now()`, note: 'Thời gian tạo']
  last_activity timestamp [not null, default: `now()`, note: 'Hoạt động cuối']
  status session_status [not null, default: 'ACTIVE', note: 'Trạng thái phiên']
  
  indexes {
    registration_id [name: 'idx_session_registration']
    device_id [name: 'idx_session_device']
    status [name: 'idx_session_status']
    last_activity [name: 'idx_session_activity']
  }
  
  Note: 'Bảng quản lý phiên làm việc'
}

// Relationships
Ref: collaborator_registration.branch_code > branch.branch_code [note: 'CTV thuộc chi nhánh']
Ref: collaborator_registration.referrer_id > referrer.id [note: 'CTV được giới thiệu bởi']
Ref: referrer.branch_code > branch.branch_code [note: 'Người giới thiệu thuộc chi nhánh']

// Business rules and constraints
Note: '''
BUSINESS RULES:
1. Chỉ cho phép làm lại đăng ký khi registration_status = 'DRAFT'
2. Không cho phép sửa đổi khi registration_status = 'PENDING_APPROVAL' trở lên
3. Bắt buộc có identity_document trước khi submit (chuyển sang PENDING_APPROVAL)
4. Mỗi registration chỉ có 1 identity_document
5. referrer_id là tùy chọn (nullable)
6. internal_email là tùy chọn (nullable)
7. Audit trail đầy đủ thông qua registration_history
8. Session management hỗ trợ tính năng "làm lại từ đầu"
'''
